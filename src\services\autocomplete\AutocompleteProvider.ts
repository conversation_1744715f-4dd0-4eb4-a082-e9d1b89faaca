import * as vscode from "vscode"
import { build<PERSON><PERSON><PERSON><PERSON><PERSON>, build<PERSON><PERSON><PERSON><PERSON><PERSON>, Api<PERSON><PERSON><PERSON>, SingleCompletionHandler } from "../../api"
import { CodeContext, ContextGatherer } from "./ContextGatherer"
import { holeFillerTemplate } from "./templating/AutocompleteTemplate"
import { AutocompleteConfigManager } from "./AutocompleteConfigManager"
import { generateImportSnippets, generateDefinitionSnippets } from "./context/snippetProvider"
import { LRUCache } from "lru-cache"
import { createDebouncedFn } from "./utils/createDebouncedFn"
import { AutocompleteDecorationAnimation } from "./AutocompleteDecorationAnimation"
import { isHumanEdit } from "./utils/EditDetectionUtils"

export const UI_UPDATE_DEBOUNCE_MS = 250
export const BAIL_OUT_TOO_MANY_LINES_LIMIT = 1000
export const MAX_COMPLETIONS_PER_CONTEXT = 5 // Per-given prefix/suffix lines, how many different per-line options to cache

// const DEFAULT_MODEL = "mistralai/codestral-2501"
const DEFAULT_MODEL = "DeepSeek-V3-0324"

export function processModelResponse(responseText: string): string {
	const fullMatch = /(<COMPLETION>)?([\s\S]*?)(<\/COMPLETION>|$)/.exec(responseText)
	if (!fullMatch) {
		return responseText
	}
	if (fullMatch[2].endsWith("</COMPLETION>")) {
		return fullMatch[2].slice(0, -"</COMPLETION>".length)
	}
	return fullMatch[2]
}

/**
 * Generates a cache key based on context's preceding and following lines
 * This is used to identify when we can reuse a previous completion
 */
function generateCacheKey({ precedingLines, followingLines }: CodeContext): string {
	const maxLinesToConsider = 5
	const precedingContext = precedingLines.slice(-maxLinesToConsider).join("\n")
	const followingContext = followingLines.slice(0, maxLinesToConsider).join("\n")
	return `${precedingContext}|||${followingContext}`
}

/**
 * Sets up autocomplete with configuration checking.
 * This function periodically checks the configuration and registers/disposes
 * the autocomplete provider accordingly.
 */
export function registerAutocomplete(context: vscode.ExtensionContext): void {
	// Initialize the config manager
	AutocompleteConfigManager.initialize(context)

	let autocompleteDisposable: vscode.Disposable | null = null
	let isCurrentlyEnabled = false

	// Function to check configuration and update provider
	const checkAndUpdateProvider = () => {
		const configManager = AutocompleteConfigManager.instance
		const shouldBeEnabled = configManager.isEnabled() && configManager.hasApiKey()

		// Only take action if the state has changed
		if (shouldBeEnabled !== isCurrentlyEnabled) {
			console.log(`🚀🔍 QAX Autocomplete enabled state changed to: ${shouldBeEnabled}`)

			autocompleteDisposable?.dispose()
			autocompleteDisposable = shouldBeEnabled ? setupAutocomplete(context) : null
			isCurrentlyEnabled = shouldBeEnabled
		}
	}

	checkAndUpdateProvider()
	const configCheckInterval = setInterval(checkAndUpdateProvider, 5000)

	// Make sure to clean up the interval when the extension is deactivated
	context.subscriptions.push({
		dispose: () => {
			clearInterval(configCheckInterval)
			autocompleteDisposable?.dispose()
		},
	})
}

function setupAutocomplete(context: vscode.ExtensionContext): vscode.Disposable {
	// State
	let enabled = true // User toggle state (default to enabled)
	let activeRequestId: string | null = null
	let shouldSkipAutocomplete = false // Flag to track when autocomplete should be skipped
	let justAcceptedSuggestion = false // Flag to track if a suggestion was just accepted
	let totalCompletionCalls = 0 // Track the total number of completion calls
	let totalAcceptedSuggestions = 0 // Track the total number of accepted suggestions

	// LRU Cache for completions
	const completionsCache = new LRUCache<string, string[]>({
		max: 50,
		ttl: 1000 * 60 * 60 * 24, // Cache for 24 hours
	})

	// Services
	const contextGatherer = new ContextGatherer()
	const animationManager = AutocompleteDecorationAnimation.getInstance()

	// Initialize API handler based on provider type
	let apiHandler: ApiHandler | null = null
	let fimHandler: SingleCompletionHandler | null = null
	const configManager = AutocompleteConfigManager.instance
	const settings = configManager.getSettings()

	if (settings.provider === "fim" && settings.fim?.apiKey && settings.fim?.baseUrl) {
		fimHandler = buildFimHandler({
			apiKey: settings.fim.apiKey,
			baseUrl: settings.fim.baseUrl,
			requestTimeoutMs: settings.requestTimeoutMs,
			customHeaders: settings.customHeaders,
			maxTokens: settings.maxTokens,
			temperature: settings.temperature,
		})
	} else if (settings.apiKey) {
		apiHandler = buildApiHandler({
			apiProvider: "openai", // Use OpenAI-compatible interface
			openAiApiKey: settings.apiKey,
			openAiBaseUrl: settings.apiBaseUrl || "https://aip.b.qianxin-inc.cn/v2",
			openAiModelId: settings.modelId || DEFAULT_MODEL,
			requestTimeoutMs: settings.requestTimeoutMs,
		})
	}

	const clearState = () => {
		vscode.commands.executeCommand("editor.action.inlineSuggest.hide")
		animationManager.stopAnimation()

		shouldSkipAutocomplete = false
		justAcceptedSuggestion = false
		activeRequestId = null
	}

	/**
	 * Check if there's a non-whitespace character immediately after the cursor
	 * This helps avoid triggering autocomplete when editing in the middle of existing code
	 */
	const isCursorSurroundedByCode = (document: vscode.TextDocument, position: vscode.Position): boolean => {
		const line = document.lineAt(position.line)

		// Check if there's a non-whitespace character immediately after the cursor
		if (position.character < line.text.length) {
			const charAfterCursor = line.text.charAt(position.character)
			return charAfterCursor !== "" && !/\s/.test(charAfterCursor)
		}

		return false
	}

	const generateFimCompletion = async ({
		codeContext: _codeContext,
		document,
		position,
	}: {
		codeContext: CodeContext
		document: vscode.TextDocument
		position: vscode.Position
	}) => {
		if (!fimHandler) {
			throw new Error("fimHandler must be set before calling generateFimCompletion!")
		}

		const requestId = crypto.randomUUID()
		activeRequestId = requestId
		animationManager.startAnimation()

		// Get text before and after cursor for FIM (limited to 100 lines total)
		const maxLines = 100
		const currentLine = position.line

		// Calculate how many lines to include before and after cursor
		const linesBeforeLimit = Math.min(currentLine, Math.floor(maxLines * 0.7)) // 70% for context before
		const linesAfterLimit = Math.min(document.lineCount - currentLine - 1, maxLines - linesBeforeLimit)

		// Get limited text before cursor
		const startLine = Math.max(0, currentLine - linesBeforeLimit)
		const textBeforeCursor = document.getText(new vscode.Range(new vscode.Position(startLine, 0), position))

		// Get limited text after cursor
		const endLine = Math.min(document.lineCount, currentLine + linesAfterLimit + 1)
		const textAfterCursor = document.getText(new vscode.Range(position, new vscode.Position(endLine, 0)))

		console.log(
			`🚀📏 FIM context: ${linesBeforeLimit} lines before, ${linesAfterLimit} lines after (total: ${linesBeforeLimit + linesAfterLimit} lines)`,
		)

		try {
			let completion = ""

			// Use streaming if available, otherwise fall back to non-streaming
			if (fimHandler.completePromptStream) {
				console.log("🚀🔄 Starting FIM streaming completion...")
				// Use streaming for better real-time experience
				for await (const chunk of fimHandler.completePromptStream(textBeforeCursor, textAfterCursor)) {
					if (activeRequestId !== requestId) {
						console.log("🚀⏹️ FIM completion cancelled (request no longer active)")
						return null // This request is no longer active
					}
					completion += chunk
					console.log(`🚀📝 FIM chunk received: "${chunk}", total: "${completion}"`)
				}
				console.log("🚀✅ FIM streaming completion finished")
			} else {
				console.log("🚀📞 Using FIM non-streaming completion...")
				// Fall back to non-streaming
				completion = await fimHandler.completePrompt(textBeforeCursor, textAfterCursor)
			}

			if (activeRequestId !== requestId) {
				return null // This request is no longer active
			}

			const processedCompletion = processModelResponse(completion)

			console.log(`🚀🎯 FIM completion generated: "${processedCompletion}"`)
			totalCompletionCalls++
			updateStatusBar()

			return processedCompletion
		} catch (error) {
			console.error("🚀❌ FIM completion error:", error)
			return null
		} finally {
			animationManager.stopAnimation()
		}
	}

	const generateCompletion = async ({
		codeContext,
		document,
		position,
	}: {
		codeContext: CodeContext
		document: vscode.TextDocument
		position: vscode.Position
	}) => {
		if (!apiHandler) {
			throw new Error("apiHandler must be set before calling generateCompletion!")
		}

		const requestId = crypto.randomUUID()
		activeRequestId = requestId
		animationManager.startAnimation()

		const snippets = [
			...generateImportSnippets(true, codeContext.imports, document.uri.fsPath),
			...generateDefinitionSnippets(true, codeContext.definitions),
		]
		const systemPrompt = holeFillerTemplate.getSystemPrompt()
		const userPrompt = holeFillerTemplate.template(codeContext, document, position, snippets)

		console.log(`🚀🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶🧶\n`, { userPrompt })

		const stream = apiHandler.createMessage(systemPrompt, [{ role: "user", content: [{ type: "text", text: userPrompt }] }])

		let completion = ""
		let processedCompletion = ""
		let lineCount = 0
		let completionCost = 0

		try {
			for await (const chunk of stream) {
				if (activeRequestId !== requestId) {
					break // This request is no longer active
				}

				if (chunk.type === "text") {
					completion += chunk.text
					processedCompletion = processModelResponse(completion)
					lineCount += processedCompletion.split("/n").length
				} else if (chunk.type === "usage") {
					completionCost = chunk.totalCost ?? 0
				}

				if (lineCount > BAIL_OUT_TOO_MANY_LINES_LIMIT) {
					processedCompletion = ""
					break
				}
			}
		} catch (error) {
			console.error("Error streaming completion:", error)
			processedCompletion = ""
		}

		// Update completion call tracking
		totalCompletionCalls++
		console.log(`🚀📊 Completion generated (${totalCompletionCalls} total calls)`)

		// Update status bar with call information
		updateStatusBar()

		if (activeRequestId === requestId) {
			animationManager.stopAnimation()
		}

		return { processedCompletion, lineCount, cost: completionCost }
	}

	// Get debounce time from settings
	const getDebounceMs = () => {
		const configManager = AutocompleteConfigManager.instance
		const settings = configManager.getSettings()
		return settings.debounceMs || UI_UPDATE_DEBOUNCE_MS
	}

	const debouncedGenerateCompletion = createDebouncedFn(generateCompletion, getDebounceMs())
	const debouncedGenerateFimCompletion = createDebouncedFn(generateFimCompletion, getDebounceMs())

	const provider: vscode.InlineCompletionItemProvider = {
		async provideInlineCompletionItems(document, position, _context, token) {
			if (!enabled || !vscode.window.activeTextEditor) {
				return null
			}

			const configManager = AutocompleteConfigManager.instance
			const settings = configManager.getSettings()

			// Check if we have valid configuration based on provider
			if (settings.provider === "fim") {
				if (!settings.fim?.apiKey || !settings.fim?.baseUrl) {
					return null
				}
				// Create or recreate the FIM handler if needed
				fimHandler =
					fimHandler ??
					buildFimHandler({
						apiKey: settings.fim.apiKey,
						baseUrl: settings.fim.baseUrl,
						requestTimeoutMs: settings.requestTimeoutMs,
						customHeaders: settings.customHeaders,
						maxTokens: settings.maxTokens,
						temperature: settings.temperature,
					})
			} else {
				if (!settings.apiKey) {
					return null
				}
				// Create or recreate the API handler if needed
				apiHandler =
					apiHandler ??
					buildApiHandler({
						apiProvider: "openai", // Use OpenAI-compatible interface
						openAiApiKey: settings.apiKey,
						openAiBaseUrl: settings.apiBaseUrl || "https://aip.b.qianxin-inc.cn/v2",
						openAiModelId: settings.modelId || DEFAULT_MODEL,
						requestTimeoutMs: settings.requestTimeoutMs,
					})
			}

			// Skip providing completions in various scenarios
			if (shouldSkipAutocomplete || justAcceptedSuggestion) {
				return null
			}

			// Skip if document has only one line
			if (document.lineCount <= 2) {
				console.log("🚀🚫 Skipping autocomplete - document has only 2or fewer real line")
				return null
			}

			// Skip if cursor is surrounded by code (editing in the middle of existing code)
			if (isCursorSurroundedByCode(document, position)) {
				console.log("🚀🚫 Skipping autocomplete - cursor surrounded by code")
				return null
			}

			// Get exactly what's been typed on the current line
			const linePrefix = document.getText(new vscode.Range(new vscode.Position(position.line, 0), position)).trimStart()
			console.log(`🚀🛑 Autocomplete for line with prefix: "${linePrefix}"!`)

			const codeContext = await contextGatherer.gatherContext(document, position, true, true)

			// Check if we have a cached completion for this context
			const cacheKey = generateCacheKey(codeContext)
			const cachedCompletions = completionsCache.get(cacheKey) ?? []
			for (const completion of cachedCompletions) {
				if (completion.startsWith(linePrefix)) {
					// Only show the remaining part of the completion
					const remainingSuffix = completion.substring(linePrefix.length)
					if (remainingSuffix.length > 0) {
						console.log(`🚀🎯 Using cached completions (${cachedCompletions.length} options)`)
						animationManager.stopAnimation()
						return [createInlineCompletionItem(remainingSuffix, position)]
					}
				}
			}

			let processedCompletion: string

			if (settings.provider === "fim") {
				const result = await debouncedGenerateFimCompletion({ document, codeContext, position })
				if (!result || token.isCancellationRequested) {
					return null
				}
				processedCompletion = result
			} else {
				const result = await debouncedGenerateCompletion({ document, codeContext, position })
				if (!result || token.isCancellationRequested) {
					return null
				}
				processedCompletion = result.processedCompletion
			}
			console.log(`🚀🛑🚀🛑🚀🛑🚀🛑🚀🛑 \n`, {
				processedCompletion,
			})

			// Cache the successful completion for future use
			if (processedCompletion) {
				const completions = completionsCache.get(cacheKey) ?? []

				// Add the new completion if it's not already in the list
				if (!completions.includes(processedCompletion)) {
					completions.push(linePrefix + processedCompletion)
					console.log(`🚀🛑 Saved new cache entry '${linePrefix + processedCompletion}'`)

					// Prune the array if it exceeds the maximum size
					// Keep the most recent completions (remove from the beginning)
					if (completions.length > MAX_COMPLETIONS_PER_CONTEXT) {
						completions.splice(0, completions.length - MAX_COMPLETIONS_PER_CONTEXT)
					}
				}
				completionsCache.set(cacheKey, completions)
			}

			return [createInlineCompletionItem(processedCompletion, position)]
		},
	}

	// Register provider and commands
	const providerDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: "**" }, provider)

	// Status bar
	const statusBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100)
	statusBar.text = "$(sparkle) QAX Complete"
	statusBar.tooltip = "QAX Code Autocomplete"
	statusBar.command = "qax-code.toggleAutocomplete"
	statusBar.show()

	// Helper function to update status bar with call and acceptance statistics
	const updateStatusBar = () => {
		if (!enabled) {
			statusBar.text = "$(circle-slash) QAX Complete"
			statusBar.tooltip = "QAX Code Autocomplete (disabled)"
			return
		}

		// Check if API key is set
		const configManager = AutocompleteConfigManager.instance
		const settings = configManager.getSettings()
		if (!settings.apiKey) {
			statusBar.text = "$(warning) QAX Complete"
			statusBar.tooltip = "A valid API key must be set to use autocomplete"
			return
		}

		const acceptanceRate =
			totalCompletionCalls > 0 ? ((totalAcceptedSuggestions / totalCompletionCalls) * 100).toFixed(1) : "0.0"
		statusBar.text = `$(sparkle) QAX Complete (${totalAcceptedSuggestions}/${totalCompletionCalls})`
		statusBar.tooltip = `\
QAX Code Autocomplete

Calls: ${totalCompletionCalls}
Accepted: ${totalAcceptedSuggestions}
Acceptance Rate: ${acceptanceRate}%
Model: ${settings.modelId || DEFAULT_MODEL}\
`
	}

	const toggleCommand = vscode.commands.registerCommand("qax-code.toggleAutocomplete", () => {
		enabled = !enabled
		updateStatusBar()
		vscode.window.showInformationMessage(`QAX Complete ${enabled ? "enabled" : "disabled"}`)
	})

	// Command to track when a suggestion is accepted
	let acceptCompletionTimer: NodeJS.Timeout | null = null
	const trackAcceptedSuggestionCommand = vscode.commands.registerCommand("qax-code.trackAcceptedSuggestion", () => {
		justAcceptedSuggestion = true
		totalAcceptedSuggestions++
		console.log(`🚀✅ Suggestion accepted (${totalAcceptedSuggestions} total accepted)`)
		updateStatusBar()

		// Set a timer to re-enable completions if no further typing occurs
		if (acceptCompletionTimer) {
			clearTimeout(acceptCompletionTimer)
		}
		acceptCompletionTimer = setTimeout(() => {
			justAcceptedSuggestion = false
			vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
			console.log("🚀🔄 Re-triggering autocomplete after acceptance")
		}, 1000) // 1000ms delay before re-triggering
	})

	// Event handlers
	const selectionHandler = vscode.window.onDidChangeTextEditorSelection((_e) => {
		// Reset the flag when selection changes
		// This ensures we only skip one completion request after accepting a suggestion
		justAcceptedSuggestion = false
	})
	const documentHandler = vscode.workspace.onDidChangeTextDocument((e) => {
		const editor = vscode.window.activeTextEditor
		if (!editor || editor.document !== e.document || !e.contentChanges.length) {
			return
		}

		clearState()

		// Consolidated trigger condition checks using EditDetectionUtils
		// This includes comprehensive detection for:
		// - Non-human edits (AI tools, extensions, etc.)
		// - Copy-paste operations (multi-line or large insertions)
		// - Backspace/delete operations
		// - Code structure patterns that suggest AI generation
		const isHumanTyping = isHumanEdit(e)
		if (!isHumanTyping) {
			shouldSkipAutocomplete = true
			console.log("🚀🤖 Skipping autocomplete trigger - non-human edit detected")
			return
		}

		// Reset flags when the user makes any valid edit
		justAcceptedSuggestion = false
		shouldSkipAutocomplete = false

		// Force inlineSuggestions to appear, even for whitespace changes
		// without this, hitting keys like spacebar won't show the completion
		vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
	})

	// Create a composite disposable to return
	const disposable = new vscode.Disposable(() => {
		providerDisposable.dispose()
		toggleCommand.dispose()
		trackAcceptedSuggestionCommand.dispose()
		statusBar.dispose()
		selectionHandler.dispose()
		documentHandler.dispose()
		animationManager.dispose()
	})

	// Still register with context for safety
	context.subscriptions.push(disposable)

	// Initialize status bar with correct state
	updateStatusBar()

	return disposable
}

/**
 * Creates an inline completion item with tracking command
 * @param completionText The text to be inserted as completion
 * @param insertRange The range where the completion should be inserted
 * @param position The position in the document
 * @returns A configured vscode.InlineCompletionItem
 */
function createInlineCompletionItem(completionText: string, position: vscode.Position): vscode.InlineCompletionItem {
	const insertRange = new vscode.Range(position, position)

	return Object.assign(new vscode.InlineCompletionItem(completionText, insertRange), {
		command: {
			command: "qax-code.trackAcceptedSuggestion",
			title: "Track Accepted Suggestion",
			arguments: [completionText, position],
		},
	})
}
