/**
 * TypeScript implementation of AST database functionality from ast_db.rs
 */

import * as sqlite3 from "sqlite3"
import * as path from "path"
import * as fs from "fs"
import { AstDefinition, AstUsage, AstStatus, AstCounters, AstError, AstErrorStats } from "../types/ast-structs"

export class AstDB {
	private db: sqlite3.Database
	private dbPath: string
	private astMaxFiles: number
	private status: AstStatus
	private counters: AstCounters
	private errorStats: AstErrorStats

	constructor(dbPath?: string, astMaxFiles: number = 10000) {
		this.dbPath = dbPath || path.join(process.cwd(), ".ast-cache", "ast.db")
		this.astMaxFiles = astMaxFiles
		this.status = {
			astate: "initializing",
			filesUnparsed: 0,
			filesTotal: 0,
			astIndexFilesTotal: 0,
			astIndexSymbolsTotal: 0,
			astIndexUsagesTotal: 0,
			astMaxFilesHit: false,
		}
		this.counters = {
			counterDefs: 0,
			counterUsages: 0,
			counterDocs: 0,
		}
		this.errorStats = {
			errors: [],
			errorsCounter: 0,
		}

		this.initializeDatabase()
	}

	private initializeDatabase(): void {
		// Ensure directory exists
		const dir = path.dirname(this.dbPath)
		if (!fs.existsSync(dir)) {
			fs.mkdirSync(dir, { recursive: true })
		}

		this.db = new sqlite3.Database(this.dbPath)
		this.createTables()
	}

	private createTables(): void {
		const createDefinitionsTable = `
      CREATE TABLE IF NOT EXISTS definitions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        official_path TEXT NOT NULL,
        symbol_type TEXT NOT NULL,
        resolved_type TEXT,
        this_is_a_class TEXT,
        this_class_derived_from TEXT,
        cpath TEXT NOT NULL,
        decl_line1 INTEGER NOT NULL,
        decl_line2 INTEGER NOT NULL,
        body_line1 INTEGER NOT NULL,
        body_line2 INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `

		const createUsagesTable = `
      CREATE TABLE IF NOT EXISTS usages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        definition_id INTEGER,
        targets_for_guesswork TEXT,
        resolved_as TEXT,
        debug_hint TEXT,
        uline INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (definition_id) REFERENCES definitions (id)
      )
    `

		const createIndexes = [
			"CREATE INDEX IF NOT EXISTS idx_definitions_cpath ON definitions(cpath)",
			"CREATE INDEX IF NOT EXISTS idx_definitions_official_path ON definitions(official_path)",
			"CREATE INDEX IF NOT EXISTS idx_usages_definition_id ON usages(definition_id)",
			"CREATE INDEX IF NOT EXISTS idx_usages_resolved_as ON usages(resolved_as)",
		]

		this.db.serialize(() => {
			this.db.run(createDefinitionsTable)
			this.db.run(createUsagesTable)
			createIndexes.forEach((index) => this.db.run(index))
		})
	}

	/**
	 * Store a definition in the database
	 */
	async storeDefinition(definition: AstDefinition): Promise<number> {
		return new Promise((resolve, reject) => {
			const stmt = this.db.prepare(`
        INSERT INTO definitions (
          official_path, symbol_type, resolved_type, this_is_a_class,
          this_class_derived_from, cpath, decl_line1, decl_line2,
          body_line1, body_line2
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

			stmt.run(
				[
					JSON.stringify(definition.officialPath),
					definition.symbolType,
					definition.resolvedType,
					definition.thisIsAClass,
					JSON.stringify(definition.thisClassDerivedFrom),
					definition.cpath,
					definition.declLine1,
					definition.declLine2,
					definition.bodyLine1,
					definition.bodyLine2,
				],
				function (err) {
					if (err) {
						reject(err)
					} else {
						const definitionId = this.lastID

						// Store associated usages
						const usagePromises = definition.usages.map((usage) => storeUsage(definitionId, usage))

						Promise.all(usagePromises)
							.then(() => resolve(definitionId))
							.catch(reject)
					}
				},
			)

			const storeUsage = (definitionId: number, usage: AstUsage): Promise<void> => {
				return new Promise((resolve, reject) => {
					const usageStmt = this.db.prepare(`
            INSERT INTO usages (
              definition_id, targets_for_guesswork, resolved_as, debug_hint, uline
            ) VALUES (?, ?, ?, ?, ?)
          `)

					usageStmt.run(
						[definitionId, JSON.stringify(usage.targetsForGuesswork), usage.resolvedAs, usage.debugHint, usage.uline],
						(err) => {
							if (err) reject(err)
							else resolve()
						},
					)
				})
			}
		})
	}

	/**
	 * Get definitions by file path
	 */
	async getDefinitionsByPath(cpath: string): Promise<AstDefinition[]> {
		return new Promise((resolve, reject) => {
			const query = `
        SELECT d.*, GROUP_CONCAT(
          json_object(
            'targetsForGuesswork', u.targets_for_guesswork,
            'resolvedAs', u.resolved_as,
            'debugHint', u.debug_hint,
            'uline', u.uline
          )
        ) as usages_json
        FROM definitions d
        LEFT JOIN usages u ON d.id = u.definition_id
        WHERE d.cpath = ?
        GROUP BY d.id
      `

			this.db.all(query, [cpath], (err, rows: any[]) => {
				if (err) {
					reject(err)
				} else {
					const definitions = rows.map((row) => this.rowToDefinition(row))
					resolve(definitions)
				}
			})
		})
	}

	/**
	 * Get definitions by official path pattern
	 */
	async getDefinitions(pathPattern: string): Promise<AstDefinition[]> {
		return new Promise((resolve, reject) => {
			const query = `
        SELECT d.*, GROUP_CONCAT(
          json_object(
            'targetsForGuesswork', u.targets_for_guesswork,
            'resolvedAs', u.resolved_as,
            'debugHint', u.debug_hint,
            'uline', u.uline
          )
        ) as usages_json
        FROM definitions d
        LEFT JOIN usages u ON d.id = u.definition_id
        WHERE d.official_path LIKE ?
        GROUP BY d.id
      `

			this.db.all(query, [`%${pathPattern}%`], (err, rows: any[]) => {
				if (err) {
					reject(err)
				} else {
					const definitions = rows.map((row) => this.rowToDefinition(row))
					resolve(definitions)
				}
			})
		})
	}

	/**
	 * Get usages for a file
	 */
	async getDocUsages(cpath: string): Promise<Array<[number, string]>> {
		return new Promise((resolve, reject) => {
			const query = `
        SELECT u.uline, u.resolved_as
        FROM usages u
        JOIN definitions d ON u.definition_id = d.id
        WHERE d.cpath = ?
        ORDER BY u.uline
      `

			this.db.all(query, [cpath], (err, rows: any[]) => {
				if (err) {
					reject(err)
				} else {
					const usages: Array<[number, string]> = rows.map((row) => [row.uline, row.resolved_as])
					resolve(usages)
				}
			})
		})
	}

	/**
	 * Clear all data for a specific file
	 */
	async clearFile(cpath: string): Promise<void> {
		return new Promise((resolve, reject) => {
			this.db.serialize(() => {
				this.db.run("DELETE FROM usages WHERE definition_id IN (SELECT id FROM definitions WHERE cpath = ?)", [cpath])
				this.db.run("DELETE FROM definitions WHERE cpath = ?", [cpath], (err) => {
					if (err) reject(err)
					else resolve()
				})
			})
		})
	}

	/**
	 * Get current status
	 */
	getStatus(): AstStatus {
		return { ...this.status }
	}

	/**
	 * Update status
	 */
	updateStatus(updates: Partial<AstStatus>): void {
		this.status = { ...this.status, ...updates }
	}

	/**
	 * Get counters
	 */
	getCounters(): AstCounters {
		return { ...this.counters }
	}

	/**
	 * Add error
	 */
	addError(errCpath: string, errLine: number, errMessage: string): void {
		if (this.errorStats.errors.length < 1000) {
			this.errorStats.errors.push({
				errCpath,
				errLine,
				errMessage,
			})
		}
		this.errorStats.errorsCounter++
	}

	/**
	 * Get error statistics
	 */
	getErrorStats(): AstErrorStats {
		return { ...this.errorStats }
	}

	/**
	 * Close database connection
	 */
	close(): void {
		this.db.close()
	}

	private rowToDefinition(row: any): AstDefinition {
		const usages: AstUsage[] = []
		if (row.usages_json) {
			try {
				const usageStrings = row.usages_json.split(",")
				for (const usageStr of usageStrings) {
					const usage = JSON.parse(usageStr)
					usages.push({
						targetsForGuesswork: JSON.parse(usage.targetsForGuesswork || "[]"),
						resolvedAs: usage.resolvedAs || "",
						debugHint: usage.debugHint || "",
						uline: usage.uline || 0,
					})
				}
			} catch (e) {
				// Handle parsing errors gracefully
			}
		}

		return {
			officialPath: JSON.parse(row.official_path || "[]"),
			symbolType: row.symbol_type,
			usages,
			resolvedType: row.resolved_type || "",
			thisIsAClass: row.this_is_a_class || "",
			thisClassDerivedFrom: JSON.parse(row.this_class_derived_from || "[]"),
			cpath: row.cpath,
			declLine1: row.decl_line1,
			declLine2: row.decl_line2,
			bodyLine1: row.body_line1,
			bodyLine2: row.body_line2,
		}
	}
}

/**
 * Static helper functions equivalent to ast_db.rs functions
 */
export class AstDBHelpers {
	/**
	 * Get definitions by path pattern
	 */
	static async definitions(astDb: AstDB, pathPattern: string): Promise<AstDefinition[]> {
		return astDb.getDefinitions(pathPattern)
	}

	/**
	 * Get document usages
	 */
	static async docUsages(astDb: AstDB, cpath: string): Promise<Array<[number, string]>> {
		return astDb.getDocUsages(cpath)
	}
}
