/**
 * AST database implementation using in-memory storage with file persistence
 * Provides storage and retrieval of AST definitions and usages for code context
 */

import * as path from "path"
import * as fs from "fs/promises"
import { AstDefinition, AstUsage, AstStatus, AstCounters, AstError, AstErrorStats } from "./ast-structs"

interface DatabaseData {
	definitions: Map<number, AstDefinition>
	usages: Map<number, AstUsage[]>
	pathIndex: Map<string, number[]>
	officialPathIndex: Map<string, number[]>
	nextId: number
}

export class AstDB {
	private data: DatabaseData
	private dbPath: string
	private astMaxFiles: number
	private status: AstStatus
	private counters: AstCounters
	private errorStats: AstErrorStats
	private saveTimeout: NodeJS.Timeout | null = null

	constructor(dbPath?: string, astMaxFiles: number = 10000) {
		this.dbPath = dbPath || path.join(process.cwd(), ".ast-cache", "ast.json")
		this.astMaxFiles = astMaxFiles
		this.status = {
			astate: "initializing",
			filesUnparsed: 0,
			filesTotal: 0,
			astIndexFilesTotal: 0,
			astIndexSymbolsTotal: 0,
			astIndexUsagesTotal: 0,
			astMaxFilesHit: false,
		}
		this.counters = {
			counterDefs: 0,
			counterUsages: 0,
			counterDocs: 0,
		}
		this.errorStats = {
			errors: [],
			errorsCounter: 0,
		}

		this.data = {
			definitions: new Map(),
			usages: new Map(),
			pathIndex: new Map(),
			officialPathIndex: new Map(),
			nextId: 1,
		}

		this.initializeDatabase()
	}

	private async initializeDatabase(): Promise<void> {
		try {
			await this.loadFromFile()
			this.status.astate = "ready"
		} catch (error) {
			console.warn("Failed to load existing database, starting fresh:", error)
			this.status.astate = "ready"
		}
	}

	private async loadFromFile(): Promise<void> {
		try {
			const data = await fs.readFile(this.dbPath, "utf-8")
			const parsed = JSON.parse(data)

			// Convert plain objects back to Maps
			this.data.definitions = new Map(parsed.definitions || [])
			this.data.usages = new Map(parsed.usages || [])
			this.data.pathIndex = new Map(parsed.pathIndex || [])
			this.data.officialPathIndex = new Map(parsed.officialPathIndex || [])
			this.data.nextId = parsed.nextId || 1

			// Update counters
			this.counters.counterDefs = this.data.definitions.size
			this.counters.counterUsages = Array.from(this.data.usages.values()).reduce((sum, usages) => sum + usages.length, 0)
			this.counters.counterDocs = new Set(Array.from(this.data.definitions.values()).map(def => def.cpath)).size
		} catch (error) {
			// File doesn't exist or is corrupted, start fresh
			throw error
		}
	}

	private async saveToFile(): Promise<void> {
		try {
			const dir = path.dirname(this.dbPath)
			await fs.mkdir(dir, { recursive: true })

			const dataToSave = {
				definitions: Array.from(this.data.definitions.entries()),
				usages: Array.from(this.data.usages.entries()),
				pathIndex: Array.from(this.data.pathIndex.entries()),
				officialPathIndex: Array.from(this.data.officialPathIndex.entries()),
				nextId: this.data.nextId,
			}

			await fs.writeFile(this.dbPath, JSON.stringify(dataToSave, null, 2))
		} catch (error) {
			console.error("Failed to save database:", error)
		}
	}

	private scheduleSave(): void {
		if (this.saveTimeout) {
			clearTimeout(this.saveTimeout)
		}
		this.saveTimeout = setTimeout(() => {
			this.saveToFile()
		}, 1000) // Save after 1 second of inactivity
	}

	/**
	 * Store a definition in the database
	 */
	async storeDefinition(definition: AstDefinition): Promise<number> {
		const definitionId = this.data.nextId++

		// Store the definition
		this.data.definitions.set(definitionId, { ...definition })

		// Store usages
		if (definition.usages.length > 0) {
			this.data.usages.set(definitionId, [...definition.usages])
		}

		// Update path index
		const pathIds = this.data.pathIndex.get(definition.cpath) || []
		pathIds.push(definitionId)
		this.data.pathIndex.set(definition.cpath, pathIds)

		// Update official path index
		const officialPathStr = definition.officialPath.join("::")
		const officialPathIds = this.data.officialPathIndex.get(officialPathStr) || []
		officialPathIds.push(definitionId)
		this.data.officialPathIndex.set(officialPathStr, officialPathIds)

		// Update counters
		this.counters.counterDefs++
		this.counters.counterUsages += definition.usages.length

		// Schedule save
		this.scheduleSave()

		return definitionId
	}

	/**
	 * Get definitions by file path
	 */
	async getDefinitionsByPath(cpath: string): Promise<AstDefinition[]> {
		const definitionIds = this.data.pathIndex.get(cpath) || []
		const definitions: AstDefinition[] = []

		for (const id of definitionIds) {
			const definition = this.data.definitions.get(id)
			if (definition) {
				const usages = this.data.usages.get(id) || []
				definitions.push({
					...definition,
					usages: [...usages]
				})
			}
		}

		return definitions
	}

	/**
	 * Get definitions by official path pattern
	 */
	async getDefinitions(pathPattern: string): Promise<AstDefinition[]> {
		const definitions: AstDefinition[] = []

		// Search through all official path entries
		for (const [officialPath, definitionIds] of this.data.officialPathIndex.entries()) {
			if (officialPath.includes(pathPattern)) {
				for (const id of definitionIds) {
					const definition = this.data.definitions.get(id)
					if (definition) {
						const usages = this.data.usages.get(id) || []
						definitions.push({
							...definition,
							usages: [...usages]
						})
					}
				}
			}
		}

		return definitions
	}

	/**
	 * Get usages for a file
	 */
	async getDocUsages(cpath: string): Promise<Array<[number, string]>> {
		const usages: Array<[number, string]> = []
		const definitionIds = this.data.pathIndex.get(cpath) || []

		for (const id of definitionIds) {
			const definitionUsages = this.data.usages.get(id) || []
			for (const usage of definitionUsages) {
				usages.push([usage.uline, usage.resolvedAs])
			}
		}

		// Sort by line number
		usages.sort((a, b) => a[0] - b[0])

		return usages
	}

	/**
	 * Clear all data for a specific file
	 */
	async clearFile(cpath: string): Promise<void> {
		const definitionIds = this.data.pathIndex.get(cpath) || []

		// Remove definitions and their usages
		for (const id of definitionIds) {
			const definition = this.data.definitions.get(id)
			if (definition) {
				// Remove from official path index
				const officialPathStr = definition.officialPath.join("::")
				const officialPathIds = this.data.officialPathIndex.get(officialPathStr) || []
				const filteredIds = officialPathIds.filter(existingId => existingId !== id)
				if (filteredIds.length > 0) {
					this.data.officialPathIndex.set(officialPathStr, filteredIds)
				} else {
					this.data.officialPathIndex.delete(officialPathStr)
				}

				// Remove definition and usages
				this.data.definitions.delete(id)
				this.data.usages.delete(id)

				// Update counters
				this.counters.counterDefs--
				const usageCount = this.data.usages.get(id)?.length || 0
				this.counters.counterUsages -= usageCount
			}
		}

		// Remove from path index
		this.data.pathIndex.delete(cpath)

		// Schedule save
		this.scheduleSave()
	}

	/**
	 * Get current status
	 */
	getStatus(): AstStatus {
		return { ...this.status }
	}

	/**
	 * Update status
	 */
	updateStatus(updates: Partial<AstStatus>): void {
		this.status = { ...this.status, ...updates }

		// Update symbol and usage counts from actual data
		if (updates.astate === "ready") {
			this.status.astIndexSymbolsTotal = this.data.definitions.size
			this.status.astIndexUsagesTotal = Array.from(this.data.usages.values())
				.reduce((sum, usages) => sum + usages.length, 0)
		}
	}

	/**
	 * Get detailed status with additional metrics
	 */
	getDetailedStatus(): AstStatus & {
		dbSizeBytes: number
		uniqueFiles: number
		averageDefinitionsPerFile: number
		averageUsagesPerDefinition: number
		lastUpdated: Date
	} {
		const baseStatus = this.getStatus()
		const uniqueFiles = new Set(Array.from(this.data.definitions.values()).map(def => def.cpath)).size
		const totalDefinitions = this.data.definitions.size
		const totalUsages = Array.from(this.data.usages.values()).reduce((sum, usages) => sum + usages.length, 0)

		return {
			...baseStatus,
			dbSizeBytes: this.estimateDbSize(),
			uniqueFiles,
			averageDefinitionsPerFile: uniqueFiles > 0 ? totalDefinitions / uniqueFiles : 0,
			averageUsagesPerDefinition: totalDefinitions > 0 ? totalUsages / totalDefinitions : 0,
			lastUpdated: new Date()
		}
	}

	/**
	 * Estimate database size in bytes
	 */
	private estimateDbSize(): number {
		let size = 0

		// Estimate definitions size
		for (const def of this.data.definitions.values()) {
			size += JSON.stringify(def).length
		}

		// Estimate usages size
		for (const usages of this.data.usages.values()) {
			size += JSON.stringify(usages).length
		}

		// Estimate index sizes
		size += JSON.stringify(Array.from(this.data.pathIndex.entries())).length
		size += JSON.stringify(Array.from(this.data.officialPathIndex.entries())).length

		return size
	}

	/**
	 * Get counters
	 */
	getCounters(): AstCounters {
		return { ...this.counters }
	}

	/**
	 * Add error
	 */
	addError(errCpath: string, errLine: number, errMessage: string): void {
		if (this.errorStats.errors.length < 1000) {
			this.errorStats.errors.push({
				errCpath,
				errLine,
				errMessage,
			})
		}
		this.errorStats.errorsCounter++
	}

	/**
	 * Get error statistics
	 */
	getErrorStats(): AstErrorStats {
		return { ...this.errorStats }
	}

	/**
	 * Close database and save any pending changes
	 */
	async close(): Promise<void> {
		if (this.saveTimeout) {
			clearTimeout(this.saveTimeout)
			this.saveTimeout = null
		}
		await this.saveToFile()
	}
}

/**
 * Static helper functions equivalent to ast_db.rs functions
 */
export class AstDBHelpers {
	/**
	 * Get definitions by path pattern
	 */
	static async definitions(astDb: AstDB, pathPattern: string): Promise<AstDefinition[]> {
		return astDb.getDefinitions(pathPattern)
	}

	/**
	 * Get document usages
	 */
	static async docUsages(astDb: AstDB, cpath: string): Promise<Array<[number, string]>> {
		return astDb.getDocUsages(cpath)
	}
}
