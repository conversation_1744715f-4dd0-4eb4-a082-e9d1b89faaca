import { VSCode<PERSON>utton, VSCodeProgressRing, VSCodeDivider } from "@vscode/webview-ui-toolkit/react"
import { memo, useCallback, useEffect, useState } from "react"
import { Database, RefreshCw, Trash2, Play, Pause, AlertCircle, CheckCircle, Clock } from "lucide-react"
import { useExtensionState } from "@/context/ExtensionStateContext"

interface ScanProgress {
	totalFiles: number
	processedFiles: number
	currentFile: string
	errors: string[]
	status: "scanning" | "parsing" | "indexing" | "complete" | "error" | "idle"
	startTime: Date
	elapsedMs: number
	estimatedRemainingMs: number
	filesPerSecond: number
	definitionsFound: number
	usagesFound: number
	bytesProcessed: number
}

interface DatabaseStatus {
	astate: "ready" | "indexing" | "error" | "initializing"
	filesTotal: number
	filesUnparsed: number
	astIndexFilesTotal: number
	astIndexSymbolsTotal: number
	astIndexUsagesTotal: number
	astMaxFilesHit: boolean
	lastUpdated?: Date
	dbSizeBytes?: number
	uniqueFiles?: number
	averageDefinitionsPerFile?: number
}

const CodebaseSettingsSection = () => {
	const [scanProgress, setScanProgress] = useState<ScanProgress | null>(null)
	const [databaseStatus, setDatabaseStatus] = useState<DatabaseStatus | null>(null)
	const [isScanning, setIsScanning] = useState(false)
	const [scanErrors, setScanErrors] = useState<string[]>([])
	const [showErrors, setShowErrors] = useState(false)

	// Load initial database status
	useEffect(() => {
		loadDatabaseStatus()
	}, [])

	const loadDatabaseStatus = useCallback(async () => {
		try {
			// TODO: Call gRPC service to get database status
			// const status = await AstServiceClient.getDatabaseStatus()
			// setDatabaseStatus(status)
			
			// Mock data for now
			setDatabaseStatus({
				astate: "ready",
				filesTotal: 150,
				filesUnparsed: 0,
				astIndexFilesTotal: 150,
				astIndexSymbolsTotal: 1250,
				astIndexUsagesTotal: 3400,
				astMaxFilesHit: false,
				lastUpdated: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
				dbSizeBytes: 1024 * 1024 * 2.5, // 2.5 MB
				uniqueFiles: 150,
				averageDefinitionsPerFile: 8.3
			})
		} catch (error) {
			console.error("Failed to load database status:", error)
		}
	}, [])

	const handleStartScan = useCallback(async () => {
		setIsScanning(true)
		setScanErrors([])
		setScanProgress({
			totalFiles: 0,
			processedFiles: 0,
			currentFile: "",
			errors: [],
			status: "scanning",
			startTime: new Date(),
			elapsedMs: 0,
			estimatedRemainingMs: 0,
			filesPerSecond: 0,
			definitionsFound: 0,
			usagesFound: 0,
			bytesProcessed: 0
		})

		try {
			// TODO: Call gRPC service to start scan
			// await AstServiceClient.startWorkspaceScan({
			//   onProgress: (progress) => setScanProgress(progress)
			// })
			
			// Mock scanning process
			mockScanProcess()
		} catch (error) {
			console.error("Failed to start scan:", error)
			setScanErrors([`Failed to start scan: ${error}`])
			setIsScanning(false)
		}
	}, [])

	const mockScanProcess = () => {
		let progress = 0
		const totalFiles = 150
		const startTime = Date.now()
		
		const interval = setInterval(() => {
			progress += Math.random() * 5 + 1
			const elapsedMs = Date.now() - startTime
			const filesPerSecond = progress / (elapsedMs / 1000)
			const estimatedRemainingMs = filesPerSecond > 0 ? ((totalFiles - progress) / filesPerSecond) * 1000 : 0
			
			setScanProgress({
				totalFiles,
				processedFiles: Math.min(progress, totalFiles),
				currentFile: `src/components/file${Math.floor(progress)}.tsx`,
				errors: [],
				status: progress >= totalFiles ? "complete" : "parsing",
				startTime: new Date(startTime),
				elapsedMs,
				estimatedRemainingMs,
				filesPerSecond,
				definitionsFound: Math.floor(progress * 8.3),
				usagesFound: Math.floor(progress * 22.7),
				bytesProcessed: Math.floor(progress * 15000)
			})
			
			if (progress >= totalFiles) {
				clearInterval(interval)
				setIsScanning(false)
				loadDatabaseStatus() // Refresh status after scan
			}
		}, 200)
	}

	const handleClearDatabase = useCallback(async () => {
		if (!confirm("Are you sure you want to clear the AST database? This action cannot be undone.")) {
			return
		}

		try {
			// TODO: Call gRPC service to clear database
			// await AstServiceClient.clearDatabase()
			setDatabaseStatus(null)
			setScanProgress(null)
		} catch (error) {
			console.error("Failed to clear database:", error)
		}
	}, [])

	const formatBytes = (bytes: number) => {
		if (bytes === 0) return "0 B"
		const k = 1024
		const sizes = ["B", "KB", "MB", "GB"]
		const i = Math.floor(Math.log(bytes) / Math.log(k))
		return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i]
	}

	const formatDuration = (ms: number) => {
		const seconds = Math.floor(ms / 1000)
		const minutes = Math.floor(seconds / 60)
		const hours = Math.floor(minutes / 60)
		
		if (hours > 0) return `${hours}h ${minutes % 60}m`
		if (minutes > 0) return `${minutes}m ${seconds % 60}s`
		return `${seconds}s`
	}

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "ready": return <CheckCircle className="w-4 h-4 text-green-500" />
			case "indexing": case "scanning": case "parsing": return <Clock className="w-4 h-4 text-blue-500" />
			case "error": return <AlertCircle className="w-4 h-4 text-red-500" />
			default: return <Database className="w-4 h-4" />
		}
	}

	const getProgressPercentage = () => {
		if (!scanProgress || scanProgress.totalFiles === 0) return 0
		return Math.round((scanProgress.processedFiles / scanProgress.totalFiles) * 100)
	}

	return (
		<div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
			{/* Header */}
			<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
				<Database className="w-5 h-5" />
				<h3 style={{ margin: 0, fontSize: "16px", fontWeight: "600" }}>
					AST Database
				</h3>
			</div>

			{/* Database Status */}
			{databaseStatus && (
				<div style={{ 
					padding: "16px", 
					border: "1px solid var(--vscode-panel-border)", 
					borderRadius: "4px",
					backgroundColor: "var(--vscode-editor-background)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "12px" }}>
						{getStatusIcon(databaseStatus.astate)}
						<span style={{ fontWeight: "500" }}>
							Status: {databaseStatus.astate.charAt(0).toUpperCase() + databaseStatus.astate.slice(1)}
						</span>
					</div>
					
					<div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "12px", fontSize: "14px" }}>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Files Indexed</div>
							<div style={{ fontWeight: "500" }}>{databaseStatus.astIndexFilesTotal}</div>
						</div>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Definitions</div>
							<div style={{ fontWeight: "500" }}>{databaseStatus.astIndexSymbolsTotal}</div>
						</div>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Usages</div>
							<div style={{ fontWeight: "500" }}>{databaseStatus.astIndexUsagesTotal}</div>
						</div>
						<div>
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>Database Size</div>
							<div style={{ fontWeight: "500" }}>
								{databaseStatus.dbSizeBytes ? formatBytes(databaseStatus.dbSizeBytes) : "Unknown"}
							</div>
						</div>
					</div>
					
					{databaseStatus.lastUpdated && (
						<div style={{ marginTop: "12px", fontSize: "12px", color: "var(--vscode-descriptionForeground)" }}>
							Last updated: {databaseStatus.lastUpdated.toLocaleString()}
						</div>
					)}
				</div>
			)}

			{/* Scan Progress */}
			{scanProgress && (
				<div style={{ 
					padding: "16px", 
					border: "1px solid var(--vscode-panel-border)", 
					borderRadius: "4px",
					backgroundColor: "var(--vscode-editor-background)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "12px" }}>
						{isScanning && <VSCodeProgressRing />}
						<span style={{ fontWeight: "500" }}>
							{scanProgress.status === "complete" ? "Scan Complete" : "Scanning Workspace"}
						</span>
						<span style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)" }}>
							{getProgressPercentage()}%
						</span>
					</div>
					
					{/* Progress Bar */}
					<div style={{ 
						width: "100%", 
						height: "8px", 
						backgroundColor: "var(--vscode-progressBar-background)",
						borderRadius: "4px",
						marginBottom: "12px",
						overflow: "hidden"
					}}>
						<div style={{
							width: `${getProgressPercentage()}%`,
							height: "100%",
							backgroundColor: "var(--vscode-progressBar-foreground)",
							transition: "width 0.3s ease"
						}} />
					</div>
					
					{/* Progress Details */}
					<div style={{ fontSize: "14px", display: "flex", flexDirection: "column", gap: "4px" }}>
						<div>
							Files: {scanProgress.processedFiles} / {scanProgress.totalFiles}
							{scanProgress.filesPerSecond > 0 && (
								<span style={{ color: "var(--vscode-descriptionForeground)", marginLeft: "8px" }}>
									({scanProgress.filesPerSecond.toFixed(1)} files/s)
								</span>
							)}
						</div>
						{scanProgress.currentFile && (
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>
								Current: {scanProgress.currentFile}
							</div>
						)}
						{scanProgress.estimatedRemainingMs > 0 && (
							<div style={{ color: "var(--vscode-descriptionForeground)" }}>
								ETA: {formatDuration(scanProgress.estimatedRemainingMs)}
							</div>
						)}
						<div>
							Found: {scanProgress.definitionsFound} definitions, {scanProgress.usagesFound} usages
						</div>
					</div>
				</div>
			)}

			{/* Action Buttons */}
			<div style={{ display: "flex", gap: "12px", flexWrap: "wrap" }}>
				<VSCodeButton 
					appearance="primary"
					onClick={handleStartScan}
					disabled={isScanning}
					style={{ display: "flex", alignItems: "center", gap: "6px" }}
				>
					{isScanning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
					{isScanning ? "Scanning..." : databaseStatus ? "Rescan Workspace" : "Scan Workspace"}
				</VSCodeButton>
				
				<VSCodeButton 
					appearance="secondary"
					onClick={loadDatabaseStatus}
					style={{ display: "flex", alignItems: "center", gap: "6px" }}
				>
					<RefreshCw className="w-4 h-4" />
					Refresh Status
				</VSCodeButton>
				
				{databaseStatus && (
					<VSCodeButton 
						appearance="secondary"
						onClick={handleClearDatabase}
						style={{ display: "flex", alignItems: "center", gap: "6px" }}
					>
						<Trash2 className="w-4 h-4" />
						Clear Database
					</VSCodeButton>
				)}
			</div>

			{/* Error Display */}
			{scanErrors.length > 0 && (
				<div style={{ 
					padding: "12px", 
					border: "1px solid var(--vscode-errorForeground)", 
					borderRadius: "4px",
					backgroundColor: "var(--vscode-inputValidation-errorBackground)"
				}}>
					<div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
						<AlertCircle className="w-4 h-4 text-red-500" />
						<span style={{ fontWeight: "500", color: "var(--vscode-errorForeground)" }}>
							Scan Errors ({scanErrors.length})
						</span>
					</div>
					{scanErrors.slice(0, 3).map((error, index) => (
						<div key={index} style={{ fontSize: "14px", color: "var(--vscode-errorForeground)", marginBottom: "4px" }}>
							{error}
						</div>
					))}
					{scanErrors.length > 3 && (
						<div style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)" }}>
							... and {scanErrors.length - 3} more errors
						</div>
					)}
				</div>
			)}

			<VSCodeDivider />

			{/* Help Text */}
			<div style={{ fontSize: "14px", color: "var(--vscode-descriptionForeground)", lineHeight: "1.4" }}>
				<p style={{ margin: "0 0 8px 0" }}>
					The AST database indexes your codebase to provide enhanced autocomplete suggestions based on your project's structure and symbols.
				</p>
				<p style={{ margin: "0" }}>
					Scanning may take a few minutes for large projects. The database will be automatically updated when you make changes to your code.
				</p>
			</div>
		</div>
	)
}

export default memo(CodebaseSettingsSection)
