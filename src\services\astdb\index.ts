/**
 * Main entry point for AST-based database and RAG implementation
 * Provides code context retrieval and autocomplete functionality
 */

export * from "./ast-structs"
export * from "./ast-db"
export * from "./completion-rag"
export * from "./workspace-scanner"
// Export logger with explicit naming to avoid conflicts
export {
	AstLogger,
	LogLevel,
	createLogger,
	defaultLogger,
	withErrorHandling,
	withRetry,
	AstError as AstErrorClass
} from "./logger"

// Re-export main classes and functions for convenience
export { AstDB, AstDBHelpers } from "./ast-db"
export {
	retrieveAstBasedExtraContext,
	SimpleTokenizer,
	EnhancedTokenizer,
	countTokens,
	getSymbolContext,
	getRelatedSymbols
} from "./completion-rag"
export { AstDefinitionHelper, SymbolType } from "./ast-structs"
export {
	WorkspaceScanner,
	scanWorkspaceForAST,
	formatScanProgress,
	createProgressReport,
	type ScanProgress,
	type ScanOptions
} from "./workspace-scanner"

// Export examples and tests
export { demonstrateAstRag, demonstrateWorkspaceScanning, runAllDemos } from "./example"
export { runTests } from "./test"
