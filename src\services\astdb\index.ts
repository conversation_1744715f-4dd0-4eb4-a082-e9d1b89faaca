/**
 * Main entry point for AST-based database and RAG implementation
 * Provides code context retrieval and autocomplete functionality
 */

export * from "./ast-structs"
export * from "./ast-db"
export * from "./completion-rag"
export * from "./workspace-scanner"

// Re-export main classes and functions for convenience
export { AstDB, AstDBHelpers } from "./ast-db"
export { retrieveAstBasedExtraContext, SimpleTokenizer, countTokens } from "./completion-rag"
export { AstDefinitionHelper, SymbolType } from "./ast-structs"
export {
	WorkspaceScanner,
	scanWorkspaceForAST,
	formatScanProgress,
	type ScanProgress,
	type ScanOptions
} from "./workspace-scanner"
