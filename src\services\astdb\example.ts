/**
 * Example usage of the AST-RAG implementation
 */

import {
	AstDB,
	retrieveAstBasedExtraContext,
	SimpleTokenizer,
	CursorPosition,
	PostprocessSettings,
	AstDefinition,
	SymbolType,
	AstDefinitionHelper,
} from "./index"

async function demonstrateAstRag() {
	console.log("=== TypeScript AST-RAG Demo ===\n")

	// Initialize AST database
	console.log("1. Initializing AST database...")
	const astDb = new AstDB("./demo-ast.db")

	try {
		// Create some sample AST definitions
		console.log("2. Adding sample definitions to database...")

		const sampleDefinitions: AstDefinition[] = [
			{
				officialPath: ["example.py", "Calculator", "add"],
				symbolType: SymbolType.Method,
				usages: [
					{
						targetsForGuesswork: ["Calculator::add"],
						resolvedAs: "example.py::Calculator::add",
						debugHint: "method call",
						uline: 15,
					},
				],
				resolvedType: "function",
				thisIsAClass: "",
				thisClassDerivedFrom: [],
				cpath: "/project/example.py",
				declLine1: 5,
				declLine2: 5,
				bodyLine1: 5,
				bodyLine2: 10,
			},
			{
				officialPath: ["utils.py", "Helper", "format_number"],
				symbolType: SymbolType.Function,
				usages: [
					{
						targetsForGuesswork: ["Helper::format_number"],
						resolvedAs: "utils.py::Helper::format_number",
						debugHint: "function call",
						uline: 20,
					},
				],
				resolvedType: "function",
				thisIsAClass: "",
				thisClassDerivedFrom: [],
				cpath: "/project/utils.py",
				declLine1: 12,
				declLine2: 12,
				bodyLine1: 12,
				bodyLine2: 18,
			},
		]

		// Store definitions in database
		for (const def of sampleDefinitions) {
			const id = await astDb.storeDefinition(def)
			console.log(`   Stored definition: ${AstDefinitionHelper.path(def)} (ID: ${id})`)
		}

		// Create tokenizer with different formats
		console.log("\n3. Testing different context formats...")

		const formats = ["starcoder", "qwen2.5", "chat"]

		for (const format of formats) {
			console.log(`\n--- Testing ${format} format ---`)

			const tokenizer = new SimpleTokenizer(format, 0.5)

			// Example cursor position
			const cursorPos: CursorPosition = {
				file: "/project/main.py",
				line: 15,
				character: 10,
			}

			// Post-processing settings
			const ppSettings: PostprocessSettings = {
				maxFilesN: 3,
				maxTokensPerFile: 200,
			}

			// Context tracking
			const contextUsed: Record<string, any> = {}

			// Retrieve context
			const extraContext = await retrieveAstBasedExtraContext(
				astDb,
				tokenizer,
				cursorPos.file,
				cursorPos,
				[Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER], // No ignore range
				ppSettings,
				500, // RAG tokens budget
				contextUsed,
				["/project"], // Project directories
			)

			console.log(`Context for ${format}:`)
			console.log(extraContext || "(empty context)")
			console.log(`Context metadata:`, JSON.stringify(contextUsed, null, 2))
		}

		// Test database queries
		console.log("\n4. Testing database queries...")

		const definitions = await astDb.getDefinitionsByPath("/project/example.py")
		console.log(`Found ${definitions.length} definitions in example.py:`)
		for (const def of definitions) {
			console.log(`   - ${AstDefinitionHelper.getName(def)} (${def.symbolType}) at lines ${def.declLine1}-${def.declLine2}`)
		}

		const usages = await astDb.getDocUsages("/project/example.py")
		console.log(`Found ${usages.length} usages in example.py:`)
		for (const [line, usage] of usages) {
			console.log(`   - Line ${line}: ${usage}`)
		}

		// Test status and counters
		console.log("\n5. Database status:")
		const status = astDb.getStatus()
		console.log(`   State: ${status.astate}`)
		console.log(`   Files: ${status.filesTotal} total, ${status.filesUnparsed} unparsed`)
		console.log(`   Index: ${status.astIndexFilesTotal} files, ${status.astIndexSymbolsTotal} symbols`)

		const counters = astDb.getCounters()
		console.log(`   Counters: ${counters.counterDefs} defs, ${counters.counterUsages} usages`)
	} catch (error) {
		console.error("Error during demo:", error)
	} finally {
		// Clean up
		console.log("\n6. Cleaning up...")
		astDb.close()
		console.log("Demo completed!")
	}
}

// Run the demo
if (require.main === module) {
	demonstrateAstRag().catch(console.error)
}

export { demonstrateAstRag }
