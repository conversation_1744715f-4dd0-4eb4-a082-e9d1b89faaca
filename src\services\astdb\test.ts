/**
 * Basic tests for AST database functionality
 */

import * as path from "path"
import * as fs from "fs/promises"
import {
	AstDB,
	AstDefinition,
	SymbolType,
	AstDefinitionHelper,
	WorkspaceScanner,
	createLogger,
	LogLevel
} from "./index"

interface TestResult {
	name: string
	passed: boolean
	error?: string
	duration: number
}

class TestRunner {
	private results: TestResult[] = []
	private logger = createLogger("TestRunner", LogLevel.DEBUG)

	async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
		const startTime = Date.now()
		try {
			this.logger.info(`Running test: ${name}`)
			await testFn()
			const duration = Date.now() - startTime
			this.results.push({ name, passed: true, duration })
			this.logger.info(`✓ ${name} (${duration}ms)`)
		} catch (error) {
			const duration = Date.now() - startTime
			this.results.push({ 
				name, 
				passed: false, 
				error: error instanceof Error ? error.message : String(error),
				duration 
			})
			this.logger.error(`✗ ${name} (${duration}ms)`, error as Error)
		}
	}

	getResults(): TestResult[] {
		return [...this.results]
	}

	getSummary(): { total: number; passed: number; failed: number; totalTime: number } {
		const total = this.results.length
		const passed = this.results.filter(r => r.passed).length
		const failed = total - passed
		const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0)
		
		return { total, passed, failed, totalTime }
	}

	printSummary(): void {
		const summary = this.getSummary()
		console.log(`\n=== Test Summary ===`)
		console.log(`Total: ${summary.total}`)
		console.log(`Passed: ${summary.passed}`)
		console.log(`Failed: ${summary.failed}`)
		console.log(`Total time: ${summary.totalTime}ms`)
		
		if (summary.failed > 0) {
			console.log(`\nFailed tests:`)
			for (const result of this.results.filter(r => !r.passed)) {
				console.log(`  ✗ ${result.name}: ${result.error}`)
			}
		}
	}
}

/**
 * Test basic database operations
 */
async function testBasicOperations(runner: TestRunner): Promise<void> {
	const testDbPath = path.join(__dirname, "test-ast.json")
	
	// Clean up any existing test database
	try {
		await fs.unlink(testDbPath)
	} catch {
		// File doesn't exist, that's fine
	}

	await runner.runTest("Database initialization", async () => {
		const astDb = new AstDB(testDbPath)
		const status = astDb.getStatus()
		if (status.astate !== "ready") {
			throw new Error(`Expected status 'ready', got '${status.astate}'`)
		}
		await astDb.close()
	})

	await runner.runTest("Store and retrieve definition", async () => {
		const astDb = new AstDB(testDbPath)
		
		const definition: AstDefinition = {
			officialPath: ["test.ts", "TestClass", "testMethod"],
			symbolType: SymbolType.Method,
			usages: [{
				targetsForGuesswork: ["TestClass::testMethod"],
				resolvedAs: "test.ts::TestClass::testMethod",
				debugHint: "method definition",
				uline: 10
			}],
			resolvedType: "function",
			thisIsAClass: "TestClass",
			thisClassDerivedFrom: [],
			cpath: path.resolve("./test.ts"),
			declLine1: 5,
			declLine2: 5,
			bodyLine1: 5,
			bodyLine2: 15
		}

		const id = await astDb.storeDefinition(definition)
		if (typeof id !== "number" || id <= 0) {
			throw new Error(`Expected positive number ID, got ${id}`)
		}

		const definitions = await astDb.getDefinitionsByPath(definition.cpath)
		if (definitions.length !== 1) {
			throw new Error(`Expected 1 definition, got ${definitions.length}`)
		}

		const retrieved = definitions[0]
		if (AstDefinitionHelper.path(retrieved) !== "test.ts::TestClass::testMethod") {
			throw new Error(`Path mismatch: ${AstDefinitionHelper.path(retrieved)}`)
		}

		await astDb.close()
	})

	await runner.runTest("Search definitions", async () => {
		const astDb = new AstDB(testDbPath)
		
		const results = await astDb.searchDefinitions("TestClass")
		if (results.length === 0) {
			throw new Error("Expected to find TestClass definition")
		}

		const testMethodResults = await astDb.searchDefinitions("testMethod")
		if (testMethodResults.length === 0) {
			throw new Error("Expected to find testMethod definition")
		}

		await astDb.close()
	})

	await runner.runTest("Database statistics", async () => {
		const astDb = new AstDB(testDbPath)
		
		const stats = astDb.getStatistics()
		if (stats.totalDefinitions === 0) {
			throw new Error("Expected at least 1 definition in statistics")
		}

		if (!stats.definitionsByType[SymbolType.Method]) {
			throw new Error("Expected method type in statistics")
		}

		await astDb.close()
	})

	await runner.runTest("Clear file", async () => {
		const astDb = new AstDB(testDbPath)
		
		const beforeStats = astDb.getStatistics()
		await astDb.clearFile(path.resolve("./test.ts"))
		const afterStats = astDb.getStatistics()
		
		if (afterStats.totalDefinitions >= beforeStats.totalDefinitions) {
			throw new Error("Expected fewer definitions after clearing file")
		}

		await astDb.close()
	})

	// Clean up test database
	try {
		await fs.unlink(testDbPath)
	} catch {
		// Ignore cleanup errors
	}
}

/**
 * Test workspace scanning (limited test)
 */
async function testWorkspaceScanning(runner: TestRunner): Promise<void> {
	await runner.runTest("Workspace scanner initialization", async () => {
		const astDb = new AstDB()
		const scanner = new WorkspaceScanner(astDb)
		
		const status = scanner.getStatus()
		if (!status) {
			throw new Error("Expected status from scanner")
		}

		await astDb.close()
	})
}

/**
 * Test error handling
 */
async function testErrorHandling(runner: TestRunner): Promise<void> {
	await runner.runTest("Invalid database path handling", async () => {
		// This should not throw, but handle gracefully
		const astDb = new AstDB("/invalid/path/that/does/not/exist/test.json")
		const status = astDb.getStatus()
		
		// Should still initialize with default state
		if (status.astate !== "ready") {
			throw new Error(`Expected graceful handling, got status: ${status.astate}`)
		}

		await astDb.close()
	})
}

/**
 * Run all tests
 */
export async function runTests(): Promise<boolean> {
	console.log("=== AST Database Tests ===\n")
	
	const runner = new TestRunner()
	
	await testBasicOperations(runner)
	await testWorkspaceScanning(runner)
	await testErrorHandling(runner)
	
	runner.printSummary()
	
	const summary = runner.getSummary()
	return summary.failed === 0
}

// Run tests if this file is executed directly
if (require.main === module) {
	runTests().then(success => {
		process.exit(success ? 0 : 1)
	}).catch(error => {
		console.error("Test runner failed:", error)
		process.exit(1)
	})
}
