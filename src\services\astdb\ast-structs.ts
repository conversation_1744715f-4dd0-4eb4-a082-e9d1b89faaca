/**
 * TypeScript implementation of AST structures from ast_structs.rs
 */

export enum SymbolType {
	Function = "function",
	Class = "class",
	Variable = "variable",
	Method = "method",
	Property = "property",
	Interface = "interface",
	Enum = "enum",
	Type = "type",
	Namespace = "namespace",
	Module = "module",
	Import = "import",
	Export = "export",
	Unknown = "unknown",
}

/**
 * Represents a usage of a symbol in code
 */
export interface AstUsage {
	/** Possible resolution targets for guesswork (e.g., ["?::DerivedFrom1::f", "?::DerivedFrom2::f"]) */
	targetsForGuesswork: string[]
	/** Final resolved path if resolution was successful */
	resolvedAs: string
	/** Debug information for resolution process */
	debugHint: string
	/** Line number where usage occurs (0-based) */
	uline: number
}

/**
 * Represents a definition of a symbol in code
 */
export interface AstDefinition {
	/** Official path of the definition (e.g., ["file", "namespace", "class", "method"]) */
	officialPath: string[]
	/** Type of the symbol */
	symbolType: SymbolType
	/** All usages associated with this definition */
	usages: AstUsage[]
	/** Resolved type information for type derivation */
	resolvedType: string
	/** If this is a class, the class name */
	thisIsAClass: string
	/** If this is a class, the base classes it derives from */
	thisClassDerivedFrom: string[]
	/** File path where the definition is located */
	cpath: string
	/** Declaration start line (1-based) */
	declLine1: number
	/** Declaration end line (1-based) */
	declLine2: number
	/** Body start line */
	bodyLine1: number
	/** Body end line */
	bodyLine2: number
}

/**
 * Helper methods for AstDefinition
 */
export class AstDefinitionHelper {
	static path(def: AstDefinition): string {
		return def.officialPath.join("::")
	}

	static pathDrop0(def: AstDefinition): string {
		if (def.officialPath.length > 3) {
			// New style long path, starts with hex code we don't want users to see
			return def.officialPath.slice(1).join("::")
		} else {
			// There's not much to cut
			return def.officialPath.join("::")
		}
	}

	static getName(def: AstDefinition): string {
		return def.officialPath[def.officialPath.length - 1] || ""
	}

	static fullLine1(def: AstDefinition): number {
		return def.declLine1
	}

	static fullLine2(def: AstDefinition): number {
		return Math.max(def.bodyLine2, def.declLine2)
	}
}

/**
 * AST database status information
 */
export interface AstStatus {
	/** Current state (e.g., "indexing", "ready") */
	astate: string
	/** Number of unparsed files */
	filesUnparsed: number
	/** Total number of files */
	filesTotal: number
	/** Total files in AST index */
	astIndexFilesTotal: number
	/** Total symbols in AST index */
	astIndexSymbolsTotal: number
	/** Total usages in AST index */
	astIndexUsagesTotal: number
	/** Whether max files limit was hit */
	astMaxFilesHit: boolean
}

/**
 * AST parsing counters
 */
export interface AstCounters {
	/** Number of definitions */
	counterDefs: number
	/** Number of usages */
	counterUsages: number
	/** Number of documents */
	counterDocs: number
}

/**
 * AST parsing error
 */
export interface AstError {
	/** File path where error occurred */
	errCpath: string
	/** Error message */
	errMessage: string
	/** Line number where error occurred */
	errLine: number
}

/**
 * AST error statistics
 */
export interface AstErrorStats {
	/** List of errors */
	errors: AstError[]
	/** Total error count */
	errorsCounter: number
}

/**
 * Context file for RAG
 */
export interface ContextFile {
	/** File name */
	fileName: string
	/** File content */
	fileContent: string
	/** Start line (1-based) */
	line1: number
	/** End line (1-based) */
	line2: number
	/** Symbols in this context */
	symbols: string[]
	/** Gradient type for ranking */
	gradientType: number
	/** Usefulness score */
	usefulness: number
}

/**
 * Cursor position
 */
export interface CursorPosition {
	/** File path */
	file: string
	/** Line number (0-based) */
	line: number
	/** Character position (0-based) */
	character: number
}

/**
 * Post-processing settings
 */
export interface PostprocessSettings {
	/** Maximum number of files to include */
	maxFilesN: number
	/** Maximum tokens per file */
	maxTokensPerFile?: number
	/** Other settings */
	[key: string]: any
}

/**
 * Tokenizer interface
 */
export interface HasTokenizerAndEot {
	/** Context format (e.g., "starcoder", "qwen2.5", "chat") */
	contextFormat: string
	/** RAG ratio for context allocation */
	ragRatio: number
	/** End of text token */
	eot: string
	/** End of sequence token */
	eos: string
}
