/**
 * AST-based RAG (Retrieval-Augmented Generation) functionality for code completion
 * Provides context retrieval based on AST analysis and symbol usage
 */

import * as path from "path"
import { AstDB, AstDBHelpers } from "./ast-db"
import {
	AstDefinition,
	ContextFile,
	CursorPosition,
	PostprocessSettings,
	HasTokenizerAndEot,
	AstDefinitionHelper,
} from "./ast-structs"

const DEBUG = false
const TAKE_USAGES_AROUND_CURSOR = 20

/**
 * Render context files into formatted string based on context format
 */
async function renderContextFiles(
	contextFormat: string,
	postprocessedMessages: ContextFile[],
	cursorFilepath: string,
	projectDirs: string[] = [],
): Promise<string> {
	if (postprocessedMessages.length === 0) {
		return ""
	}

	const repoName = projectDirs.length > 0 ? path.basename(projectDirs[0]) : "default_repo"

	const cursorFilepathStripped = projectDirs.length > 0 ? path.relative(projectDirs[0], cursorFilepath) : cursorFilepath

	let contextFilesPrompt = ""

	switch (contextFormat) {
		case "starcoder":
			contextFilesPrompt += `<repo_name>${repoName}\n`
			for (const m of postprocessedMessages) {
				contextFilesPrompt += `<file_sep>${m.fileName}\n${m.fileContent}`
			}
			return `${contextFilesPrompt}<file_sep>${cursorFilepathStripped}\n`

		case "qwen2.5":
			contextFilesPrompt += `<|repo_name|>${repoName}\n`
			for (const m of postprocessedMessages) {
				contextFilesPrompt += `<|file_sep|>${m.fileName}\n${m.fileContent}`
			}
			return `${contextFilesPrompt}<|file_sep|>${cursorFilepathStripped}\n`

		case "chat":
			for (const m of postprocessedMessages) {
				contextFilesPrompt += `Filename: ${m.fileName}\nUseful content:\n\`\`\`\n${m.fileContent}\n\`\`\`\n\n`
			}
			return contextFilesPrompt

		default:
			console.warn(`context_format "${contextFormat}" not recognized`)
			return ""
	}
}

/**
 * Convert cursor position to context files based on AST usages
 */
async function cursorPositionToContextFile(
	astDb: AstDB,
	cpath: string,
	cursorLine: number,
	contextUsed: Record<string, any>,
): Promise<ContextFile[]> {
	if (cursorLine < 0 || cursorLine > 65535) {
		console.error(`cursor line ${cursorLine} out of range`)
		return []
	}

	const cursorLineOneBased = cursorLine + 1 // Convert to 1-based
	const usages = await AstDBHelpers.docUsages(astDb, cpath)

	// Calculate distances and sort by proximity to cursor
	const distances: Array<[number, string, number]> = usages.map(([line, usage]) => {
		const distance = Math.abs(line - cursorLineOneBased)
		return [distance, usage, line]
	})

	distances.sort((a, b) => a[0] - b[0])

	const nearestUsages = distances
		.slice(0, TAKE_USAGES_AROUND_CURSOR)
		.map(([, usage, line]) => [line, usage] as [number, string])

	if (DEBUG) {
		console.log("nearest_usages", nearestUsages)
	}

	// Get unique paths from usages
	const uniquePaths = new Set(nearestUsages.map(([, doubleColonPath]) => doubleColonPath))

	const output: ContextFile[] = []
	const bucketDeclarations: any[] = []

	for (const doubleColonPath of uniquePaths) {
		if (DEBUG) {
			console.log(`adding ${doubleColonPath} to context`)
		}

		try {
			const defs = await AstDBHelpers.definitions(astDb, doubleColonPath)

			if (defs.length !== 1) {
				console.warn(`hmm, number of definitions for ${doubleColonPath} is ${defs.length} which is not one`)
			}

			for (const def of defs) {
				output.push({
					fileName: def.cpath,
					fileContent: "",
					line1: AstDefinitionHelper.fullLine1(def),
					line2: AstDefinitionHelper.fullLine2(def),
					symbols: [AstDefinitionHelper.pathDrop0(def)],
					gradientType: 4,
					usefulness: 100.0,
				})

				const usageDict = {
					file_path: def.cpath,
					line1: AstDefinitionHelper.fullLine1(def),
					line2: AstDefinitionHelper.fullLine2(def),
					name: AstDefinitionHelper.pathDrop0(def),
				}
				bucketDeclarations.push(usageDict)
			}
		} catch (error) {
			console.error(`Error getting definitions for ${doubleColonPath}:`, error)
		}
	}

	contextUsed.bucket_declarations = bucketDeclarations

	if (DEBUG) {
		console.log("FIM context", output)
	}

	return output
}

/**
 * Simple post-processing function to limit context files
 */
async function postprocessContextFiles(
	contextFileVec: ContextFile[],
	ragTokensN: number,
	ppSettings: PostprocessSettings,
): Promise<ContextFile[]> {
	// Filter out files with negative usefulness (banned files)
	let filtered = contextFileVec.filter((file) => file.usefulness >= 0)

	// Sort by usefulness (descending)
	filtered.sort((a, b) => b.usefulness - a.usefulness)

	// Limit by max files
	if (ppSettings.maxFilesN > 0) {
		filtered = filtered.slice(0, ppSettings.maxFilesN)
	}

	// Simple token-based filtering (assuming average 4 chars per token)
	const estimatedTokensPerChar = 0.25
	let totalTokens = 0
	const result: ContextFile[] = []

	for (const file of filtered) {
		const estimatedTokens = file.fileContent.length * estimatedTokensPerChar
		if (totalTokens + estimatedTokens <= ragTokensN) {
			result.push(file)
			totalTokens += estimatedTokens
		} else {
			break
		}
	}

	return result
}

/**
 * Main function to retrieve AST-based extra context for code completion
 */
export async function retrieveAstBasedExtraContext(
	astDb: AstDB | null,
	tokenizer: HasTokenizerAndEot,
	cpath: string,
	pos: CursorPosition,
	subblockToIgnoreRange: [number, number],
	ppSettings: PostprocessSettings,
	ragTokensN: number,
	contextUsed: Record<string, any>,
	projectDirs: string[] = [],
): Promise<string> {
	console.log(" -- ast-based rag search starts --")

	let modifiedPpSettings = { ...ppSettings }
	if (modifiedPpSettings.maxFilesN === 0) {
		modifiedPpSettings.maxFilesN = 5
	}

	const ragT0 = Date.now()
	let astContextFileVec: ContextFile[] = []

	if (astDb) {
		try {
			astContextFileVec = await cursorPositionToContextFile(astDb, cpath, pos.line, contextUsed)
		} catch (error) {
			console.error("Error in cursorPositionToContextFile:", error)
		}
	}

	const toBucketsMs = Date.now() - ragT0

	// Add FIM ban region if specified
	if (subblockToIgnoreRange[0] !== Number.MAX_SAFE_INTEGER && subblockToIgnoreRange[1] !== Number.MIN_SAFE_INTEGER) {
		const fimBan: ContextFile = {
			fileName: cpath,
			fileContent: "",
			line1: subblockToIgnoreRange[0] + 1, // Convert to 1-based
			line2: subblockToIgnoreRange[1] + 1, // Convert to 1-based
			symbols: [],
			gradientType: 4,
			usefulness: -1.0, // Mark as banned
		}
		astContextFileVec.push(fimBan)
	}

	console.log(" -- post processing starts --")
	const postT0 = Date.now()

	const postprocessedMessages = await postprocessContextFiles(astContextFileVec, ragTokensN, modifiedPpSettings)

	const ragMs = Date.now() - ragT0
	const postMs = Date.now() - postT0

	console.log(` -- /post buckets ${toBucketsMs}ms, post ${postMs}ms -- `)

	// Store context information for debugging
	contextUsed.attached_files = postprocessedMessages.map((x) => ({
		file_name: x.fileName,
		file_content: x.fileContent,
		line1: x.line1,
		line2: x.line2,
	}))
	contextUsed.rag_ms = ragMs

	return await renderContextFiles(tokenizer.contextFormat, postprocessedMessages, cpath, projectDirs)
}

/**
 * Simple tokenizer interface for testing
 */
export class SimpleTokenizer implements HasTokenizerAndEot {
	contextFormat: string
	ragRatio: number
	eot: string
	eos: string

	constructor(contextFormat: string = "starcoder", ragRatio: number = 0.5, eot: string = "<|endoftext|>", eos: string = "") {
		this.contextFormat = contextFormat
		this.ragRatio = ragRatio
		this.eot = eot
		this.eos = eos
	}
}

/**
 * Helper function to count tokens (simplified implementation)
 */
export function countTokens(text: string): number {
	// Simple approximation: 4 characters per token on average
	return Math.ceil(text.length / 4)
}

/**
 * Get context files for a specific symbol
 */
export async function getSymbolContext(
	astDb: AstDB,
	symbolName: string,
	maxFiles: number = 5
): Promise<ContextFile[]> {
	const definitions = await astDb.searchDefinitions(symbolName, maxFiles)
	const contextFiles: ContextFile[] = []

	for (const definition of definitions) {
		contextFiles.push({
			fileName: definition.cpath,
			fileContent: "", // Content would be loaded separately
			line1: definition.declLine1,
			line2: definition.bodyLine2,
			symbols: [definition.officialPath.join("::")],
			gradientType: 4,
			usefulness: 100.0
		})
	}

	return contextFiles
}

/**
 * Get related symbols based on usage patterns
 */
export async function getRelatedSymbols(
	astDb: AstDB,
	symbolPath: string,
	maxResults: number = 10
): Promise<string[]> {
	const relatedSymbols = new Set<string>()

	// Find definitions that match the symbol path
	const definitions = await astDb.getDefinitions(symbolPath)

	for (const definition of definitions) {
		// Add symbols from the same file
		const fileDefinitions = await astDb.getDefinitionsByPath(definition.cpath)
		for (const fileDef of fileDefinitions) {
			if (fileDef.officialPath.join("::") !== symbolPath) {
				relatedSymbols.add(fileDef.officialPath.join("::"))
			}
		}

		// Add symbols from usages
		for (const usage of definition.usages) {
			if (usage.resolvedAs && usage.resolvedAs !== symbolPath) {
				relatedSymbols.add(usage.resolvedAs)
			}
		}

		if (relatedSymbols.size >= maxResults) {
			break
		}
	}

	return Array.from(relatedSymbols).slice(0, maxResults)
}

/**
 * Enhanced tokenizer with better token counting
 */
export class EnhancedTokenizer implements HasTokenizerAndEot {
	contextFormat: string
	ragRatio: number
	eot: string
	eos: string
	private tokensPerChar: number

	constructor(
		contextFormat: string = "starcoder",
		ragRatio: number = 0.5,
		eot: string = "<|endoftext|>",
		eos: string = "",
		tokensPerChar: number = 0.25
	) {
		this.contextFormat = contextFormat
		this.ragRatio = ragRatio
		this.eot = eot
		this.eos = eos
		this.tokensPerChar = tokensPerChar
	}

	/**
	 * More accurate token counting
	 */
	countTokens(text: string): number {
		// Simple heuristic: count words, punctuation, and special tokens
		const words = text.split(/\s+/).filter(w => w.length > 0)
		const punctuation = (text.match(/[.,;:!?(){}[\]]/g) || []).length
		const specialTokens = (text.match(/<\|[^|]+\|>/g) || []).length

		return words.length + punctuation + specialTokens
	}

	/**
	 * Estimate tokens for context planning
	 */
	estimateTokens(text: string): number {
		return Math.ceil(text.length * this.tokensPerChar)
	}
}

/**
 * Example usage function
 */
export async function exampleUsage() {
	// Initialize AST database
	const astDb = new AstDB()

	// Create tokenizer
	const tokenizer = new SimpleTokenizer("starcoder", 0.5)

	// Example cursor position
	const cursorPos: CursorPosition = {
		file: "/path/to/file.py",
		line: 10,
		character: 5,
	}

	// Post-processing settings
	const ppSettings: PostprocessSettings = {
		maxFilesN: 5,
		maxTokensPerFile: 500,
	}

	// Context tracking
	const contextUsed: Record<string, any> = {}

	try {
		const extraContext = await retrieveAstBasedExtraContext(
			astDb,
			tokenizer,
			cursorPos.file,
			cursorPos,
			[Number.MAX_SAFE_INTEGER, Number.MIN_SAFE_INTEGER], // No ignore range
			ppSettings,
			1000, // RAG tokens budget
			contextUsed,
			["/path/to/project"], // Project directories
		)

		console.log("Extra context:", extraContext)
		console.log("Context used:", contextUsed)
	} catch (error) {
		console.error("Error in example usage:", error)
	} finally {
		await astDb.close()
	}
}
